package at.aau.se2.cluedo.controllers;

import at.aau.se2.cluedo.dto.GameStartedResponse;
import at.aau.se2.cluedo.dto.StartGameRequest;
import at.aau.se2.cluedo.dto.TurnStateResponse;
import at.aau.se2.cluedo.models.gamemanager.GameManager;
import at.aau.se2.cluedo.models.gameobjects.Player;
import at.aau.se2.cluedo.models.lobby.Lobby;
import at.aau.se2.cluedo.services.GameService;
import at.aau.se2.cluedo.services.LobbyService;

import java.util.List;
import java.util.Map;

import at.aau.se2.cluedo.services.TurnService;
import at.aau.se2.cluedo.services.TurnService.TurnState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

@Controller
public class GameController {

    private static final Logger logger = LoggerFactory.getLogger(GameController.class);

    @Autowired
    private GameService gameService;

    @Autowired
    private LobbyService lobbyService;
    @Autowired
    private TurnService turnService;

    @MessageMapping("/startGame/{lobbyId}")
    @SendTo("/topic/gameStarted/{lobbyId}")
    public GameStartedResponse startGame(@DestinationVariable String lobbyId, StartGameRequest request) {
        Player initiator = request.getPlayer();


        Lobby lobby = lobbyService.getLobby(lobbyId);

        Player lobbyPlayer = lobby.getPlayers().stream()
                .filter(p -> p.getName().equals(initiator.getName()))
                .findFirst()
                .orElse(null);

        if (lobbyPlayer == null) {

            throw new IllegalStateException("Player not found in lobby");
        }

        if (!lobby.getHostId().equals(lobbyPlayer.getPlayerID())) {

            throw new IllegalStateException("Only the host can start the game");
        }

        if (!gameService.canStartGame(lobbyId)) {

            throw new IllegalStateException("Not enough players to start a game. Minimum required: 3");
        }

        try {
            GameManager gameManager = gameService.startGameFromLobby(lobbyId);

            List<Player> gamePlayers = gameManager.getPlayers();

            turnService.initializeTurnState(lobbyId);
            logger.info("Game started and turn system initialized for lobby: {}", lobbyId);

            return (new GameStartedResponse(lobbyId, gamePlayers));
        } catch (Exception e) {
            throw new IllegalStateException("Failed to start game: " + e.getMessage());
        }
    }
}
